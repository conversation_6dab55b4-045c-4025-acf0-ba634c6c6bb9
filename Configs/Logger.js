const winston = require("winston")
require("winston-daily-rotate-file")

const { VALUES } = require("./constants")

const {
    combine,
    errors,
    timestamp,
    colorize,
    printf,
} = winston.format

// logger options
const datePattern = "DD-MM-YYYY"
const maxSize = "128m"
const maxFiles = "7d"
const utc = true

const customFormat = printf(info => {
    const {
        timestamp,
        level,
        message,
        ...meta
    } = info

    const ts = timestamp.slice(0, 19).replace('T', ' ')

    return `${ts} [${level.toUpperCase()}]: ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 4) + "\n" : ''}`
})

const errorFilter = winston.format((info, opts) => {
    return info.level === "error" ? info : false
})

const infoAndWarnFilter = winston.format((info, opts) => {
    return info.level === "info" || info.level === "warn" ? info : false
})

const httpFilter = winston.format((info, opts) => {
    return info.level === "http" ? info : false
})

// Separate warn/error
const transports = [
    new winston.transports.DailyRotateFile({
        name: "Error Logs",
        filename: "Logs/hawak-app-error-%DATE%.log",
        datePattern,
        maxSize,
        maxFiles,
        level: "warn",
        utc,
        json: true,
        colorize: false,
        format: combine(
            errorFilter(),
        )
    }),
    new winston.transports.DailyRotateFile({
        name: "INFO logs",
        filename: "Logs/hawak-app-%DATE%.log",
        datePattern,
        maxSize,
        maxFiles,
        level: "info",
        utc,
        json: true,
        colorize: false,
        format: combine(
            infoAndWarnFilter(),
        )
    }),
    new winston.transports.DailyRotateFile({
        name: "HTTP logs",
        filename: "Logs/hawak-app-http-%DATE%.log",
        datePattern,
        maxSize,
        maxFiles,
        level: "http",
        utc,
        json: true,
        colorize: false,
        format: combine(
            httpFilter(),
        )
    }),
    new (winston.transports.Console)({
        level: "debug",
        utc,
        handleExceptions: true,
        format: combine(
            colorize({ all: true }),
        )
    }),
]

// Log unhandled exceptions to separate file
const exceptionHandlers = [
    new winston.transports.DailyRotateFile({
        name: "Exception Logs",
        filename: "Logs/hawak-app-exceptions-%DATE%.log",
        datePattern,
        maxSize,
        maxFiles,
        utc,
    })
]

const logger = winston.createLogger({
    transports,
    exceptionHandlers,
    level: "debug",
    defaultMeta: {
        // service: 'user-service',
    },
    // Default format, which is applied to all transports
    format: combine(
        errors({ stack: true }),
        timestamp(),
        customFormat,
    )
})

global.logger = logger
