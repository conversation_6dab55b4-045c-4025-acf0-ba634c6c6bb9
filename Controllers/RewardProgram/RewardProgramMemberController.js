const RewardProgramMemberModel = new (require("../../Models/RewardProgram/RewardProgramMemberModel"))();
const RewardProgramProductClaimsModel = new (require("../../Models/RewardProgram/RewardProgramProductClaimsModel"))();
const RewardProgramPointsLogModel = new (require("../../Models/RewardProgram/RewardProgramPointsLogModel"))();
const RewardProgramMemberExpiryPointsModel = new (require("../../Models/RewardProgram/RewardProgramMemberExpiryPointsModel"))();
const CommonModel = new (require("../../Models/common"))();

const SAPServiceModel = new (require("../../Models/SAPServiceModel"))();
const TenantPortalModal = new (require("../../Models/tenantPortal"))();

const RewardProgramController = new (require("./RewardProgramController"))()

const {
    sentEffectiveRewardMemberToSQS
} = require("../../SQS/SQSSentEvents")

const {
    REWARD_PROGRAM,
    ENTITY_STATUS,
    STATUS_CODES
} = require('../../Configs/constants');

module.exports = class {

    enrollMembers = async (req, res) => {
        try {
            const {
                tenantId,
                customerUserRoleIds,
                customerPaymentTermInfo,
                effectiveDate,
                rewardProgramId,
                timezone
            } = req.body

            const activeMembers = await RewardProgramMemberModel.findMembersByFilter(
                {
                    customerUserRoleIds,
                    tenantId,
                    status: ENTITY_STATUS.ACTIVE
                },
                {
                    _id: 0,
                    member_id: 1,
                },
                {
                    lean: true,
                    populate: [
                        {
                            path: "customer_user_role_id",
                            select: "-_id customer_legal_name"
                        },
                        {
                            path: "reward_program_id",
                            select: "-_id name"
                        },
                    ]
                }
            )

            if (activeMembers.length) {
                return res.handler.validationError("customers_already_in_reward_program", activeMembers)
            }

            const isToday = moment.tz(effectiveDate, "YYYY/MM/DD", timezone).isSame(moment.tz(timezone), 'day');
            if (!isToday) {
                const member = await RewardProgramMemberModel.findMemberByFilter({
                    customerUserRoleIds,
                    tenantId,
                })
                if (member) {
                    return res.handler.validationError("customers_previously_enrolled_with_effective_date", activeMembers)
                }
            }

            await CommonModel.transactionCallback(async (session) => {
                await RewardProgramMemberModel.addMembers(
                    customerUserRoleIds,
                    rewardProgramId,
                    tenantId,
                    req.headers.userDetails._id,
                    session
                )

                await TenantPortalModal.updateBulkCustomers(
                    {
                        _id: {
                            $in: customerUserRoleIds
                        }
                    },
                    {
                        customer_payment_term_info: {
                            _id: customerPaymentTermInfo.id,
                            number_of_days: customerPaymentTermInfo.numberOfDays
                        }
                    },
                    req.headers,
                    {
                        session
                    }
                )

                if (!isToday) {
                    await Promise.all(customerUserRoleIds.map(customerUserRoleId => {
                        return sentEffectiveRewardMemberToSQS({
                            customerUserRoleId,
                            rewardProgramId,
                            timezone,
                            tenantId,
                            effectiveDate
                        })
                    }))
                }
            })

            return res.handler.success("customers_enrolled_in_reward_program")
        }
        catch (error) {
            if (
                error.code === STATUS_CODES.MONGODB_DUPLICATE_KEY_CODE &&
                "unique_member_id" in error.keyValue
            ) {
                return res.handler.conflict("member_id_already_exists")
            }

            return res.handler.serverError(error);
        }
    }

    deactivateMembers = async (req, res) => {
        try {
            await CommonModel.transactionCallback(async (session) => {
                const claim = await RewardProgramProductClaimsModel.findClaim(
                    {
                        ...req.query,
                        claimStatus: [
                            REWARD_PROGRAM.REWARD.CLAIM.STATUS.PENDING,
                            REWARD_PROGRAM.REWARD.CLAIM.STATUS.PROCESSING,
                            REWARD_PROGRAM.REWARD.CLAIM.STATUS.SHIPPED,
                        ]
                    },
                    "_id",
                    {
                        session,
                        lean: true
                    }
                )
                if (claim) {
                    return res.handler.conflict("reward_program_member_claim_exists")
                }

                const members = await RewardProgramMemberModel.findMembersByFilter(
                    {
                        ...req.query,
                        status: ENTITY_STATUS.ACTIVE
                    },
                    undefined,
                    {
                        session,
                    }
                )
                if (!members.length)
                    return res.handler.notFound("reward_program_members_not_found")

                for (let i = 0; i < members.length; i++) {
                    const memberInfo = members[i];

                    if (memberInfo.coins.remaining) {
                        await RewardProgramPointsLogModel.addPointsLog(
                            {
                                tenantId: memberInfo.tenant_id,
                                customerUserRoleId: memberInfo.customer_user_role_id._id,
                                rewardProgramMemberId: memberInfo._id,
                                rewardProgramId: memberInfo.reward_program_id,
                                pointType: REWARD_PROGRAM.POINT.TYPE.COINS,
                                entryType: REWARD_PROGRAM.POINT.ENTRY_TYPE.EXPIRED,
                                logType: REWARD_PROGRAM.POINT.LOG_TYPE.EXPIRED.COINS,
                                points: memberInfo.coins.remaining,
                            },
                            memberInfo,
                            req.headers.userDetails._id,
                            session
                        )
                    }
                    if (memberInfo.vip_points.remaining) {
                        await RewardProgramPointsLogModel.addPointsLog(
                            {
                                tenantId: memberInfo.tenant_id,
                                customerUserRoleId: memberInfo.customer_user_role_id._id,
                                rewardProgramMemberId: memberInfo._id,
                                rewardProgramId: memberInfo.reward_program_id,
                                pointType: REWARD_PROGRAM.POINT.TYPE.VIP_POINTS,
                                entryType: REWARD_PROGRAM.POINT.ENTRY_TYPE.EXPIRED,
                                logType: REWARD_PROGRAM.POINT.LOG_TYPE.EXPIRED.COINS,
                                points: memberInfo.vip_points.remaining,
                            },
                            memberInfo,
                            req.headers.userDetails._id,
                            session
                        )
                    }
                }

                await RewardProgramMemberExpiryPointsModel.deleteExpiryPoints(
                    req.query,
                    {
                        session
                    }
                )

                await RewardProgramMemberModel.updateMembers(
                    req.query,
                    {
                        is_enrolled: false
                    },
                    req.headers.userDetails._id,
                    {
                        session
                    }
                )

                return res.handler.success("customers_removed_from_reward_program")
            })
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    listMembers = async (req, res) => {
        try {
            const data = await RewardProgramMemberModel.findMembersWithPagination({
                ...req.query,
                status: ENTITY_STATUS.ACTIVE
            })

            return res.handler.success(undefined, data)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    memberDetails = async (req, res) => {
        try {
            let rewardProgramSelect = {
                milestones: 1,
                vip_rules: 1
            }
            if (req.query.usedForMobile) {
                rewardProgramSelect = {
                    milestones: 1,
                    vip_rules: 1,
                    base_amount: 1,
                    classic_member_coin_rules: 1,
                    vip_member_coin_rules: 1,
                    vip_points_rules: 1,
                }
            }

            const memberInfo = await RewardProgramMemberModel.findMemberByFilter(
                {
                    ...req.query,
                    status: ENTITY_STATUS.ACTIVE
                },
                undefined,
                {
                    populate: [
                        {
                            path: "reward_program_id",
                            select: rewardProgramSelect
                        },
                    ],
                    lean: true
                }
            )

            return res.handler.success(undefined, memberInfo)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    countMembers = async (req, res) => {
        try {
            const count = await RewardProgramMemberModel.getMemberCountByMembership(req.query)
            return res.handler.success(undefined, count)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    updateCustomersRewardProgramMembership = async () => {
        try {
            var profiler = logger.startTimer()
            logger.info("Started: Update customers reward program membership cron job")

            await RewardProgramController.rewardProgramsCallBackBasedTenants(async (
                rewardProgram,
                sapIntegrationCredentials,
            ) => {
                const rewardProgramMembersFilter = {
                    rewardProgramId: rewardProgram._id,
                    tenantId: rewardProgram.tenant_id,
                }

                await RewardProgramMemberModel.membersCallBackWithPagination(
                    rewardProgramMembersFilter,
                    this.updateMembershipByRewardMember(
                        rewardProgram,
                        sapIntegrationCredentials,
                    )
                )
            })
        }
        catch (error) {
            logger.error(error)
        }
        finally {
            profiler.done({ message: "UPDATE CUSTOMERS REWARD PROGRAM MEMBERSHIP" })
            logger.info(`Completed: Update customers reward program membership cron job\n`)
        }
    }

    updateMembershipByRewardMember = (
        rewardProgram,
        sapIntegrationCredentials,
        date
    ) => async (memberInfo) => {
        const agingDate = parseInt(moment(date).format("YYYYMMDD"))
        const currentYear = parseInt(moment(date).subtract(1, "months").format("YYYY"))
        const currentMonth = parseInt(moment(date).subtract(1, "months").format("MM"))

        const aging = await SAPServiceModel.getBalance(
            {
                customerExternalId: memberInfo.customer_user_role_id.external_id,
                agingDate: agingDate,
            },
            sapIntegrationCredentials
        )

        if (!aging) {
            return
        }

        const onTimePayment = SAPServiceModel.checkPaymentsOnTime(
            memberInfo.customer_user_role_id.customer_payment_term_info.number_of_days,
            aging
        )

        memberInfo.payment_history.unshift({
            year: currentYear,
            month: currentMonth,
            on_time_payment: onTimePayment
        })
        memberInfo.payment_history = memberInfo.payment_history.slice(0, 12)

        await memberInfo.save()

        logger.info(`On time payment ${onTimePayment} for ${currentMonth}/${currentYear} of reward member id : ${memberInfo._id} in tenant: ${rewardProgram.tenant_id}`);

        switch (memberInfo.membership) {
            case REWARD_PROGRAM.MEMBER.MEMBERSHIP_TYPE.CLASSIC: {
                if (memberInfo.vip_points.remaining >= rewardProgram.vip_rules.upgrade_points) {
                    if (memberInfo.payment_history.length >= 4) {
                        if (
                            memberInfo.payment_history[0].on_time_payment &&
                            memberInfo.payment_history[1].on_time_payment &&
                            memberInfo.payment_history[2].on_time_payment &&
                            memberInfo.payment_history[3].on_time_payment
                        ) {
                            await CommonModel.transactionCallback(async (session) => {
                                memberInfo.membership = REWARD_PROGRAM.MEMBER.MEMBERSHIP_TYPE.VIP

                                await RewardProgramPointsLogModel.addPointsLog(
                                    {
                                        tenantId: rewardProgram.tenant_id,
                                        customerUserRoleId: memberInfo.customer_user_role_id._id,
                                        rewardProgramMemberId: memberInfo._id,
                                        rewardProgramId: rewardProgram._id,
                                        pointType: REWARD_PROGRAM.POINT.TYPE.VIP_POINTS,
                                        entryType: REWARD_PROGRAM.POINT.ENTRY_TYPE.CLAIMED,
                                        logType: REWARD_PROGRAM.POINT.LOG_TYPE.VIP.UPGRADE,
                                        points: rewardProgram.vip_rules.upgrade_points,
                                    },
                                    memberInfo,
                                    undefined,
                                    session
                                )
                            })
                        }
                    }
                }

                break
            }
            case REWARD_PROGRAM.MEMBER.MEMBERSHIP_TYPE.VIP: {
                if (memberInfo.vip_membership_expiry?.year) {
                    if (
                        memberInfo.vip_membership_expiry.month <= currentMonth &&
                        memberInfo.vip_membership_expiry.year <= currentYear
                    ) {
                        memberInfo.membership = REWARD_PROGRAM.MEMBER.MEMBERSHIP_TYPE.CLASSIC
                        memberInfo.vip_membership_expiry = null
                        await memberInfo.save()
                    }
                }
                else {
                    if (memberInfo.payment_history.length >= 4) {
                        if (
                            memberInfo.payment_history[0].on_time_payment ||
                            memberInfo.payment_history[1].on_time_payment ||
                            memberInfo.payment_history[2].on_time_payment ||
                            memberInfo.payment_history[3].on_time_payment
                        ) {
                            const vipMembershipExpireAt = moment(memberInfo.vip_membership_achieved_at).add(rewardProgram.vip_rules.expiry_duration ?? 12, 'months').toDate()
                            if (vipMembershipExpireAt < new Date()) {
                                if (memberInfo.vip_points.remaining >= rewardProgram.vip_rules.renew_points) {
                                    await RewardProgramPointsLogModel.addPointsLog(
                                        {
                                            tenantId: rewardProgram.tenant_id,
                                            customerUserRoleId: memberInfo.customer_user_role_id._id,
                                            rewardProgramMemberId: memberInfo._id,
                                            rewardProgramId: rewardProgram._id,
                                            pointType: REWARD_PROGRAM.POINT.TYPE.VIP_POINTS,
                                            entryType: REWARD_PROGRAM.POINT.ENTRY_TYPE.CLAIMED,
                                            logType: REWARD_PROGRAM.POINT.LOG_TYPE.VIP.RENEW,
                                            points: rewardProgram.vip_rules.renew_points,
                                        },
                                        memberInfo,
                                    )
                                    memberInfo.vip_membership_achieved_at = new Date();
                                    await memberInfo.save()
                                }
                                else {
                                    memberInfo.membership = REWARD_PROGRAM.MEMBER.MEMBERSHIP_TYPE.CLASSIC
                                    await memberInfo.save()
                                }
                            }
                        }
                        else {
                            memberInfo.membership = REWARD_PROGRAM.MEMBER.MEMBERSHIP_TYPE.CLASSIC
                            await memberInfo.save()
                        }
                    }
                }

                break
            }
        }
    }


}
