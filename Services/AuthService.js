const IntegrationCredentialModel = new (require('../Models/IntegrationCredentialModel'))();
const MessageBird = new (require('../Configs/messageBird'))();
const { INTEGRATION_CHANNELS, TENANT_WHATSAPP_TIMEZONES_TEMPLATE } = require('../Configs/constants');

class AuthService {

    async findIntegrationCredentialModel(tenantId) {
        const response = await IntegrationCredentialModel.getCredential({
            tenant_id: tenantId,
            name: INTEGRATION_CHANNELS.MESSAGE_BIRD,
        });
        const config = response?.configurations;
        return ['access_key', 'base_url', 'workspace_id', 'channel_id']
            .every(field => config?.[field]?.trim()) ? config : null;
    }

    async sendWhatsAppMessage(configurations, phoneNumber, OTP) {
        try {
            const template = await MessageBird.getProjectDetail(
                TENANT_WHATSAPP_TIMEZONES_TEMPLATE,
                configurations
            );
            const payload = {
                sender: {
                    connector: {
                        identifierValue: configurations.channel_id,
                    }
                },
                receiver: {
                    contacts: [
                        {
                            identifierKey: 'phonenumber',
                            identifierValue: "+919630799684"
                        }
                    ]
                },
                template: {
                    projectId: template.data.projectId,
                    version: template.data.id,
                    locale: template.data.defaultLocale || 'en',
                    variables: {
                        otp: OTP,
                    },
                }
            };
            const response = await MessageBird.sendMessageToReceiver(payload, configurations);
            return response;
        } catch (error) {
            console.error('Failed to send WhatsApp message:', error.message || error);
            return null;
        }
    }

}

module.exports = AuthService