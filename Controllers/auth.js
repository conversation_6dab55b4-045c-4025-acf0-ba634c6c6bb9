const {
    SIGNING_TYPE,
    FILE_PATH, VALUES,
    BY_PASS_OTP,
    APP_OTP_VALIDITY_IN_MINUTES,
    deviceTypeDetected,
    TENANT_SERVICES,
    SEND_EMAIL_NUMBERS,
    PRIMITIVE_ROLES,
    TENANT_WHATSAPP_TIMEZONES,
} = require("../Configs/constants");

const jwt = require('jsonwebtoken');

const UserModal = new (require("../Models/auth"))();
const UserRoleDeviceModel = new (require("../Models/UserRoleDeviceModel"))();
const TenantPortalModal = new (require("../Models/tenantPortal"));
const CommonController = new (require("./common/common"))();
const AuthService = new (require("../Services/AuthService"))()


const { generateOtp, stringifyObjectId } = require('../Utils/helpers');
const encrypt = new (require('../Configs/encrypt'))();
const FileUpload = require('../Configs/awsUploader').S3Upload;
const fileUpload = new FileUpload();

class AuthController {
    async Signup(req, res) {
        try {
            let userExists = await UserModal.findUserByMobileNumber(req.body.mobileNumber, req.body.countryCode);
            if (userExists) {
                return res.handler.custom(STATUS_CODES.CONFLICT, 'validation_exists_email');
            }

            userExists = await UserModal.findUserByEmail(req.body.email);
            if (userExists) {
                return res.handler.custom(STATUS_CODES.CONFLICT, 'validation_exists_user');
            }
            // create the user
            const user = await UserModal.signup(req.body);
            const cognitoData = await UserModal.creatUserInUserPool(req.body);
            // save the username provided in the user-pool in MongoDB
            user.cognito_username = cognitoData.userSub;
            await user.save();

            await Promise.all([UserModal.adminConfirmSignUp(req.body), UserModal.verifyUserDetails(req.body)]);

            return res.handler.success();
        } catch (error) {
            return res.handler.serverError(error);
        }

    }

    async testingEmail(req, res) {
        try {
            // await UserModal.sendOtpVerificationEMail({ username: "Bhargav Gajjar", otp_num: 6452, rtl: true }, "ar", "<EMAIL>");
            // await UserModal.resetPasswordLinkEmail({username: "Shridutt", reset_link: `${VALUES.WEB_APP_URL}auth/create-new-password`, rtl: true}, "<EMAIL>", "verification link", "ar" );
            await UserModal.welcomeCustomer(undefined, undefined, "<EMAIL>");
            return res.handler.success();
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async generateMapFile(req, res) {
        try {
            const response = await fetch(`${process.env.MAP_STATIC_API_BASE_URL}?
        center=${`${req.query.lat || 0.00},${req.query.lng || 0.00}`}
        &key=${process.env.MAP_STATIC_API_KEY}
        &zoom=${process.env.MAP_IMAGE_ZOOM_LEVEL}
        &size=${process.env.MAP_SIZE}
        &markers=${process.env.MAP_MARKER}${`${req.query.lat || 0.00},${req.query.lng || 0.00}`}`);

            let contentType = response.headers.get("Content-Type");
            const buffer = await response.buffer();
            res.writeHead(200, {
                'Content-Type': contentType,
                'Content-Length': buffer.length
            });


            return res.end(buffer);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async SignIn(req, res) {
        try {
            console.log("req.body",req.body)
            let userDetails;
            if (req.body.type === SIGNING_TYPE.MOBILE) {
                userDetails = await UserModal.findUserByMobileNumber(req.body.mobileNumber, req.body.countryCode);
                if (!userDetails) {
                    return res.handler.unauthorized("un_authorized_not_valid_credentials_mobile");
                }
                req.body.userId = userDetails._id;
                req.body.userName = userDetails.email

            } else {
                userDetails = await UserModal.findUserByEmail(req.body.email);
                console.log("🚀 ~ AuthController ~ SignIn ~ userDetails:", userDetails)

                if (!userDetails) {
                    return res.handler.unauthorized("un_authorized_not_valid_credentials_email");
                }

                req.body.userName = req.body.email;
            }
            if (userDetails.is_deleted) {
                return res.handler.conflict("validation_deleted_user")
            }
            if (!userDetails.is_active) {
                return res.handler.conflict("validation_in_active_user")
            }
            let needsVerification = false;

            const tokens = await UserModal.signIn(req.body);

            const userRolesPromise = UserModal.getNonDeletedPortalUserRolesById(userDetails._id, req.headers.devicetype);
            const deviceTokenInfoPro = UserModal.getUserDeviceInfo(userDetails._id, req.headers.devicetoken);
            const [userRoles, deviceTokenInfo] = await Promise.all([userRolesPromise, deviceTokenInfoPro]);

            if (userDetails.profile_pic) {
                userDetails.profile_pic = await fileUpload.getSignedUrl(FILE_PATH.USER_PROFILE, userDetails.profile_pic);
            }

            for (let i = 0; i < userRoles.length; i++) {
                const {
                    role_id: {
                        name: roleName
                    },
                    profile_pic,
                    tenant_id: {
                        _id: tenantId
                    } = {},
                } = userRoles[i]

                if (profile_pic) {
                    userRoles[i].profile_pic = CommonController.getProfileImageUrl(tenantId) + profile_pic;
                    userRoles[i].profile_thumbnail_pic = CommonController.getProfileThumbImageUrl(tenantId) + profile_pic;
                }

                if ((roleName === PRIMITIVE_ROLES.SUPER_ADMIN) && !VALUES.IS_DEV_ENV) {
                    needsVerification = true;
                    break
                }
            }
            if (!needsVerification && !deviceTokenInfo) {
                needsVerification = true;
            } else if (deviceTokenInfo && (!deviceTokenInfo.device_verified)) {
                needsVerification = true;
            }

            await UserModal.inActivatePreviousUserSessions(userDetails);
            if (userRoles.length > 1) {
                await UserModal.startUserSession(req.headers.devicetoken, req.headers.devicetype, tokens, userDetails);
            } else if (userRoles.length === 1) {

                if (!userRoles[0].is_active) {
                    return res.handler.notFound("validation_no_active_user_role");
                }

                if (userRoles[0].role_id.portal_type === VALUES.portals.BRANCH_PORTAL) {
                    await UserModal.startUserSessionWithUserRoleId(req.headers.devicetoken, req.headers.devicetype, tokens, userDetails, userRoles[0], undefined, userRoles[0].branch_id._id)
                } else if (userRoles[0].role_id.portal_type === VALUES.portals.TENANT_PORTAL) {
                    await UserModal.startUserSessionWithUserRoleId(req.headers.devicetoken, req.headers.devicetype, tokens, userDetails, userRoles[0], userRoles[0].tenant_id._id, undefined)
                } else {
                    await UserModal.startUserSessionWithUserRoleId(req.headers.devicetoken, req.headers.devicetype, tokens, userDetails, userRoles[0], undefined, undefined);
                }
            } else {
                return res.handler.notFound("validation_not_found_user_role");
            }

            if (needsVerification) {
                await UserModal.modifyDeviceTokenInfo(req.headers.devicetoken, userDetails, req.body.type)
            }

            // logout from other profiles before login in into other profile
            return res.handler.custom(STATUS_CODES.SUCCESS, 'status_success', {
                tokens, userRoles, needsVerification,
                userDetails: { _id: userDetails._id, first_name: userDetails.first_name, last_name: userDetails.last_name, profile_pic: userDetails.profile_pic, email: userDetails.email, country_code: userDetails.country_code, mobile_number: userDetails.mobile_number }
            })
        } catch (error) {
            switch (error.name) {
                case "NotAuthorizedException":
                    return res.handler.unauthorized("un_authorized_not_valid_credentials_email");

                default:
                    return res.handler.serverError(error);
            }

        }
    }

    async appSignIn(req, res) {
        try {
            const {
                mobileNumber,
                countryCode,
            } = req.body;

            const {
                deviceaccesstype,
                devicetoken
            } = req.headers

            const [userDetails, customerDetails] = await Promise.all([
                UserModal.findUserByMobileNumber(mobileNumber, countryCode),
                TenantPortalModal.findTenantCustomerByMobile(countryCode, mobileNumber),
            ]);

            if (!userDetails && !customerDetails) {
                return res.handler.unauthorized("mobile_not_exists");
            }

            const appProfiles = await UserModal.mobileAppProfileWithFilter(
                userDetails?._id,
                customerDetails?._id,
                false,
                deviceaccesstype,
            );

            const {
                profiles,
                hasInactiveProfile,
                restrictedCustomerAppProfile,
                deviceLimitExceeds,
            } = this.filterAppProfileByDeviceToken(
                appProfiles,
                devicetoken,
                deviceaccesstype,
            );

            if (!profiles.length) {
                if (hasInactiveProfile) {
                    return res.handler.badRequest("profile_inactive");
                }
                else if (restrictedCustomerAppProfile) {
                    return res.handler.badRequest("app_access_not_active");
                }
                else if (deviceLimitExceeds) {
                    return res.handler.badRequest('device_limit_exceeded');
                }
                else {
                    return res.handler.unauthorized("mobile_not_exists");
                }
            }

            const tenantId = profiles[0].tenant_id._id
            const userTimezone = profiles[0]?.tenant_id?.country?.timezone;
            // const isOtpSentOnWhatsapp = TENANT_WHATSAPP_TIMEZONES.includes(userTimezone);
            const isOtpSentOnWhatsapp = true

            // const result = await UserModal.adminInitAUthForAppUser(userDetails.email);
            const country_code = userDetails?.country_code || customerDetails.country_code;
            const mobile_number = userDetails?.mobile_number || customerDetails.mobile_number;

            // TODO: need to remove
            const otp = generateOtp();;

            let appOtp = await UserModal.addAppLoginOtp();
            appOtp.country_code = customerDetails?.country_code || userDetails.country_code;
            appOtp.mobile_number = customerDetails?.mobile_number || userDetails.mobile_number;
            appOtp.otp = otp
            appOtp.otp_generation_time = new Date();

            let userEmail

            const customerData = profiles.find(customer => {
                return stringifyObjectId(customer.user_id) === stringifyObjectId(customerDetails?._id)
            })

            if (userDetails?.email) {
                userEmail = userDetails?.email
            }
            else if (customerData?.customer_email) {
                if (SEND_EMAIL_NUMBERS.includes(mobile_number)) {
                    userEmail = customerData.customer_email
                }
            }

            appOtp.email = userEmail

            await appOtp.save();

            if (VALUES.IS_DEV_ENV) {
                let isEmailSent = false
                let isSmsSent = false
                let isWhatsAppSent = false

                if (userEmail) {
                    try {
                        await UserModal.sendEmailOTP(userEmail, otp, "App login Verification OTP")
                        isEmailSent = true
                    }
                    catch (emailError) {
                        logger.error(emailError)
                    }
                }
                if (isOtpSentOnWhatsapp) {
                    try {
                        const configurations = await AuthService.findIntegrationCredentialModel(tenantId);
                        if (!configurations) {
                            return res.handler.notFound('message_bird_credentials_not_found');
                        }
                        await AuthService.sendWhatsAppMessage(
                            configurations,
                            `${country_code}${mobile_number}`,
                            otp
                        )
                        isWhatsAppSent = true
                    }
                    catch (whatsAppError) {
                        logger.error(whatsAppError)
                    }

                    if (!isEmailSent && !isWhatsAppSent) {
                        if (userEmail) {
                            return res.handler.badRequest("unable_to_send_otp_whatsapp_or_email")
                        }
                        else {
                            return res.handler.badRequest("unable_to_send_otp_whatsapp")
                        }
                    }
                }
                else {
                    try {
                        await UserModal.sendMobileOTP((country_code + mobile_number).replace("+", ""), otp);
                        isSmsSent = true
                    }
                    catch (smsError) {
                        logger.error(smsError)
                    }

                    if (!isEmailSent && !isSmsSent) {
                        if (userEmail) {
                            return res.handler.badRequest("unable_to_send_otp_sms_or_email")
                        }
                        else {
                            return res.handler.badRequest("unable_to_send_otp_sms")
                        }
                    }
                }
            }

            return res.handler.success(null, { Session: appOtp._id });
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async appResendOTP(req, res) {
        try {
            const {
                deviceaccesstype,
            } = req.headers

            const body = req.body;
            const otpSession = await UserModal.getAppOtpWithFilter({ _id: body.session });

            if (!otpSession) {
                return res.handler.notFound("invalid_valid_session");
            }

            if (moment().diff(moment(otpSession.otp_generation_time), "minutes") > APP_OTP_VALIDITY_IN_MINUTES - 1) {
                const otp = generateOtp();
                otpSession.otp = otp;
                otpSession.otp_generation_time = new Date();
                await otpSession.save();
            }

            let mobileNumber = otpSession.mobile_number
            let countryCode = otpSession.country_code

            const [userDetails, customerDetails] = await Promise.all([
                UserModal.findUserByMobileNumber(mobileNumber, countryCode),
                TenantPortalModal.findTenantCustomerByMobile(countryCode, mobileNumber),
            ]);

            if (!userDetails && !customerDetails) {
                return res.handler.unauthorized("mobile_not_exists");
            }

            const appProfiles = await UserModal.mobileAppProfileWithFilter(
                userDetails?._id,
                customerDetails?._id,
                false,
                deviceaccesstype,
            );

            const tenantId = appProfiles[0].tenant_id._id
            const userTimezone = appProfiles[0]?.tenant_id?.country?.timezone;
            // const isOtpSentOnWhatsapp = TENANT_WHATSAPP_TIMEZONES.includes(userTimezone);
            const isOtpSentOnWhatsapp = true

            let isEmailSent = false
            let isSmsSent = false
            let isWhatsAppSent = false

            if (otpSession.email) {
                try {
                    await UserModal.sendEmailOTP(otpSession.email, otpSession.otp, "App login Verification OTP")
                    isEmailSent = true
                }
                catch (emailError) {
                    logger.error(emailError)
                }
            }

            if (isOtpSentOnWhatsapp) {
                const configurations = await AuthService.findIntegrationCredentialModel(tenantId);
                if (!configurations) {
                    return res.handler.notFound('message_bird_credentials_not_found');
                }
                await AuthService.sendWhatsAppMessage(
                    configurations,
                    `${otpSession.country_code}${otpSession.mobile_number}`,
                    otpSession.otp
                )
                isWhatsAppSent = true

                if (!isEmailSent && !isWhatsAppSent) {
                    if (otpSession.email) {
                        return res.handler.badRequest("unable_to_send_otp_whatsapp_or_email")
                    }
                    else {
                        return res.handler.badRequest("unable_to_send_otp_whatsapp")
                    }
                }
            }
            else {
                try {
                    await UserModal.sendMobileOTP((otpSession.country_code + otpSession.mobile_number).replace("+", ""), otpSession.otp);
                    isSmsSent = true
                }
                catch (smsError) {
                    logger.error(smsError)
                }
                if (!isEmailSent && !isSmsSent) {
                    if (otpSession.email) {
                        return res.handler.badRequest("unable_to_send_otp_sms_or_email")
                    }
                    else {
                        return res.handler.badRequest("unable_to_send_otp_sms")
                    }
                }
            }

            return res.handler.success();

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    /**
     *
     * @param {Array} userProfiles ( all application profiles )
     * @param {String} deviceToken
     */
    filterAppProfileByDeviceToken(userProfiles = [], devicetoken, deviceaccesstype) {
        const profiles = [];
        let deviceLimitExceeds = false;
        let hasInactiveProfile = false;
        let restrictedCustomerAppProfile = false;

        userProfiles.forEach(profile => {
            if (!profile.is_active) {
                hasInactiveProfile = true
            }
            else if (profile.role_id.portal_type === VALUES.portals.SALES_APP || profile.role_id.portal_type === VALUES.portals.TENANT_PORTAL || profile.role_id.portal_type === VALUES.portals.SUPERVISOR_APP) {
                hasInactiveProfile = false
                profiles.push(profile)
                return;
            }
            else if (profile.role_id.portal_type === VALUES.portals.CUSTOMER_APP) {
                hasInactiveProfile = false
                let mobileCount = 0;
                let tabletCount = 0;
                let devices = profile?.device_access || [];

                if (!profile.customer_app_access || !profile.tenant_id.services?.find(s => s.key === TENANT_SERVICES.CUSTOMER_APP)?.permission?.view) {
                    restrictedCustomerAppProfile = true;
                    return;
                }

                if (devices.length) {
                    for (let i = 0; i < devices.length; i++) {
                        let d = devices[i];
                        if (d.type === VALUES.deviceAccessType.MOBILE) {
                            mobileCount++
                        } else if (d.type === VALUES.deviceAccessType.TABLET) {
                            tabletCount++
                        }

                        if (d.device_id === devicetoken) {
                            profiles.push(profile);
                            return;
                        }
                    }
                }

                if (mobileCount === 0 && VALUES.deviceAccessType.MOBILE === deviceaccesstype) {
                    profiles.push(profile);
                    return;
                }

                if (tabletCount === 0 && VALUES.deviceAccessType.TABLET === deviceaccesstype) {
                    profiles.push(profile);
                    return;
                }

                if (VALUES.deviceAccessType.MOBILE === deviceaccesstype && mobileCount > 0) {
                    deviceLimitExceeds = true;
                }

                if (VALUES.deviceAccessType.TABLET === deviceaccesstype && tabletCount > 0) {
                    deviceLimitExceeds = true;
                }
            }
        });

        return {
            profiles,
            deviceLimitExceeds,
            hasInactiveProfile,
            restrictedCustomerAppProfile,
        };
    }

    async verifyAppAuthOtp(req, res) {
        try {
            const body = req.body;
            const sessionData = await UserModal.getAppOtpWithFilter({ _id: req.body.session });
            if (!sessionData) {
                return res.handler.notFound("invalid_valid_session");
            }

            if (moment().diff(moment(sessionData.otp_generation_time), "minutes") > APP_OTP_VALIDITY_IN_MINUTES) {
                await sessionData.deleteOne();
                return res.handler.badRequest("otp_expired");
            }

            if ((String(sessionData.mobile_number) !== body.mobileNumber) || (sessionData.country_code !== body.countryCode)) {
                return res.handler.badRequest("invalid_otp");
            }

            if (body.otp !== String(BY_PASS_OTP) && body.otp !== sessionData.otp) {
                return res.handler.badRequest("invalid_otp");
            }

            const [userDetails, customerDetails] = await Promise.all(
                [
                    UserModal.findUserByMobileNumber(sessionData.mobile_number, sessionData.country_code),
                    TenantPortalModal.findTenantCustomerByMobile(sessionData.country_code, sessionData.mobile_number),
                ]
            );

            const userProfiles = await UserModal.mobileAppProfileWithFilter(
                userDetails?._id,
                customerDetails?._id,
                true,
                req.headers.deviceaccesstype
            );

            const {
                profiles,
                deviceLimitExceeds,
                hasInactiveProfile,
                restrictedCustomerAppProfile,
            } = this.filterAppProfileByDeviceToken(
                userProfiles,
                req.headers.devicetoken,
                req.headers.deviceaccesstype,
            );

            if (!profiles.length) {
                if (hasInactiveProfile) {
                    return res.handler.badRequest("profile_inactive");
                } else if (restrictedCustomerAppProfile) {
                    return res.handler.badRequest("app_access_not_active");
                } else if (deviceLimitExceeds) {
                    return res.handler.badRequest('device_limit_exceeded');
                } else {
                    return res.handler.unauthorized("mobile_not_exists");
                }
            }
            const payload = {};
            payload["mobile_number"] = customerDetails?.mobile_number || userDetails?.mobile_number;
            payload["country_code"] = customerDetails?.country_code || userDetails?.country_code;
            payload["tenant_customer"] = customerDetails?.toObject();
            payload["user"] = userDetails?.toObject();
            payload['deviceaccesstype'] = req.headers.deviceaccesstype;
            let accessToken = jwt.sign(payload, process.env.JWT_TOKEN_SECRET, { expiresIn: process.env.JWT_EXPIRE_IN_HOURS });
            const refreshToken = jwt.sign(payload, process.env.REFRESH_TOKEN_SECRET, { expiresIn: process.env.REFRESH_EXPIRE_IN_DAYS });

            if (profiles.length > 1) {
                const collection_name = customerDetails ? "tenant_customers" : "users"
                await UserModal.startUserSession(req.headers.devicetoken, req.headers.devicetype, { accessToken, tokenInfo: { exp: process.env.JWT_EXPIRE_IN_HOURS.split('h')[0] * 3600000 } }, customerDetails || userDetails, collection_name);
            } else if (profiles.length === 1) {

                if (!profiles[0].is_active) {
                    return res.handler.notFound("validation_no_active_user_role");
                }

                const isCustomerProfile = profiles[0].role_id.portal_type === VALUES.portals.CUSTOMER_APP

                if (isCustomerProfile) {
                    let addDeviceFlag = false;
                    let existingDevice = false;
                    let mobDevices = 0;
                    let tabDevices = 0;
                    if (Array.isArray(profiles[0].device_access)) {
                        for (let i = 0; i < profiles[0].device_access.length; i++) {
                            let d = profiles[0].device_access[i];

                            if (VALUES.deviceAccessType.MOBILE === d.type) {
                                mobDevices++;
                            } else if (VALUES.deviceAccessType.TABLET === d.type) {
                                tabDevices++;
                            }

                            if (d.device_id === req.headers.devicetoken) {
                                existingDevice = true;
                                break;
                            }
                        }

                        if (req.headers.deviceaccesstype === VALUES.deviceAccessType.MOBILE && mobDevices === 0 && !existingDevice) {
                            addDeviceFlag = true
                        } else if (req.headers.deviceaccesstype === VALUES.deviceAccessType.TABLET && tabDevices === 0 && !existingDevice) {
                            addDeviceFlag = true
                        }


                    } else {
                        addDeviceFlag = true;
                    }

                    if (!existingDevice && !addDeviceFlag) {
                        return res.handler.notFound("no_valid_device_found");
                    }
                    if (addDeviceFlag) {
                        await TenantPortalModal.addCustomerDeviceAccess({ userRoleId: profiles[0]._id, deviceId: req.headers.devicetoken, deviceType: req.headers.deviceaccesstype, deviceOs: req.headers.devicetype })
                    }
                } else if (profiles[0].role_id.portal_type === VALUES.portals.SALES_APP) {
                    //TODO: need to implement multiple device & ask for login into current device or cancel
                }

                if (req.body.fcmToken) {
                    await UserRoleDeviceModel.upsertDevice(
                        {
                            device_type: req.headers.devicetype,
                            device_token: req.headers.devicetoken,
                        },
                        {
                            tenant_id: profiles[0].tenant_id?._id,
                            user_role_id: profiles[0]._id,
                            fcm_token: req.body.fcmToken
                        }
                    )
                }

                const loginTime = new Date();
                payload['login_time'] = loginTime.toISOString();

                const profileFilter = {};
                const updateObj = {};

                accessToken = jwt.sign(payload, process.env.JWT_TOKEN_SECRET, { expiresIn: process.env.JWT_EXPIRE_IN_HOURS });

                if (payload.deviceaccesstype === VALUES.deviceAccessType.MOBILE) {
                    profileFilter._id = profiles[0]._id;
                    updateObj.last_mobile_login_time = loginTime;

                    updateObj.$min = {
                        first_mobile_login_time: loginTime
                    }
                }
                else if (payload.deviceaccesstype === VALUES.deviceAccessType.TABLET) {
                    profileFilter._id = profiles[0]._id;
                    updateObj.last_tablet_login_time = loginTime;
                }

                await Promise.all([
                    UserModal.startUserSessionWithUserRoleId(
                        req.headers.devicetoken,
                        req.headers.devicetype,
                        {
                            accessToken: accessToken,
                            tokenInfo: {
                                exp: process.env.JWT_EXPIRE_IN_HOURS.split('h')[0] * 3600000
                            }
                        },
                        isCustomerProfile
                            ? customerDetails
                            : userDetails,
                        profiles[0],
                        profiles[0].tenant_id?._id,
                        undefined,
                    ),
                    UserModal.updateUserRoleByFilter(profileFilter, updateObj),
                ]);

            } else {
                return res.handler.notFound("validation_not_found_user_role");
            }
            await sessionData.deleteOne();
            const tokens = { accessToken, refreshToken };
            return res.handler.success(null, { tokens, userRoles: profiles, userDetails, customerDetails });
        } catch (error) {
            switch (error.code) {
                case 'NotAuthorizedException':
                    return res.handler.badRequest("session_not_valid");
                    break;

                default:
                    return res.handler.serverError(error);
            }
        }
    }

    async appUserRoleList(req, res) {
        try {
            const body = req.body;

            const [userDetails, customerDetails] = await Promise.all(
                [
                    UserModal.findUserByMobileNumber(body.mobileNumber, body.countryCode),
                    TenantPortalModal.findTenantCustomerByMobile(body.countryCode, body.mobileNumber),
                ]);
            if (!userDetails && !customerDetails) {
                return res.handler.unauthorized("mobile_not_exists");
            }

            const appProfiles = await UserModal.mobileAppProfileWithFilter(userDetails?._id, customerDetails?._id, true, req.headers.deviceaccesstype);
            const { profiles } = this.filterAppProfileByDeviceToken(appProfiles, req.headers.devicetoken, req.headers.deviceaccesstype);

            if (!profiles.length) {
                return res.handler.unauthorized("mobile_not_exists");
            }

            return res.handler.success(null, { userRoles: profiles, userDetails, customerDetails });
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async verifyUserOTP(req, res) {
        try {
            const isValidOtp = await UserModal.verifyUserOTP(req.body, req.headers);
            if (!isValidOtp) {
                return res.handler.badRequest("validation_device_incorrect_otp");
            }
            return res.handler.success("validation_device_verified");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async resendOTP(req, res) {
        try {
            const userSession = await UserModal.findUserDevice(req.headers.devicetoken, req.headers.userDetails._id);
            console.log("🚀 ~ AuthController ~ resendOTP ~ userSession:", userSession)
            if (!userSession) {
                return res.handler.badRequest("validation_not_found_could_not_find_valid_session");
            }
            const otp = generateOtp();
            const userDetails = req.headers.userDetails;

            if (req.body.sendOtpTo === SIGNING_TYPE.MOBILE) {
                const [
                    smsResult,
                    mailResult
                ] = await Promise.allSettled([
                    UserModal.sendMobileOTP((userDetails.country_code + userDetails.mobile_number).replace("+", ""), otp),
                    UserModal.sendOtpVerificationEMail({ username: userDetails.first_name + " " + userDetails.last_name, otp_num: otp, rtl: false }, undefined, userDetails.email)
                ])

                if (smsResult.reason) {
                    logger.error(smsResult.reason)
                }

                if (mailResult.reason) {
                    logger.error(mailResult.reason)
                }

                if (smsResult.status === "rejected" && mailResult.status === "rejected") {
                    return res.handler.badRequest("unable_to_send_otp_sms_or_email")
                }
            }
            else if (req.body.sendOtpTo === SIGNING_TYPE.EMAIL) {
                await UserModal.sendOtpVerificationEMail({ username: userDetails.first_name + " " + userDetails.last_name, otp_num: otp, rtl: false }, undefined, userDetails.email)
            }

            userSession.otp = otp;
            await userSession.save();
            if (req.body.sendOtpTo === SIGNING_TYPE.MOBILE) {
                return res.handler.success("process_sms_sent");
            } else {
                return res.handler.success("process_email_sent");
            }
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async forgotPassword(req, res) {
        try {
            //
            let otp;
            let userDetails;
            if (req.body.sendOtpTo === SIGNING_TYPE.MOBILE) {
                otp = generateOtp();
                // otp = encrypt.generateAuthToken();
                userDetails = await UserModal.findUserByMobileNumber(req.body.mobileNumber, req.body.countryCode);
                if (!userDetails) {
                    return res.handler.notFound("validation_not_found_mobile");
                }
                userDetails.forgot_password_otp = otp;
                userDetails.otp_verification_time = new Date();
                userDetails.otp_verification_time_limit = 10;

                const [
                    smsResult,
                    mailResult
                ] = await Promise.allSettled([
                    UserModal.sendMobileOTP((req.body.countryCode + req.body.mobileNumber).replace("+", ""), otp),
                    UserModal.sendOtpVerificationEMail({ username: userDetails.first_name + " " + userDetails.last_name, otp_num: otp, rtl: false }, undefined, userDetails.email)
                ])

                if (smsResult.reason) {
                    logger.error(smsResult.reason)
                }

                if (mailResult.reason) {
                    logger.error(mailResult.reason)
                }

                if (smsResult.status === "rejected" && mailResult.status === "rejected") {
                    return res.handler.badRequest("unable_to_send_otp_sms_or_email")
                }

                await userDetails.save();
                // await UserModal.sendMobileVerification((req.body.countryCode + req.body.mobileNumber).replace("+", ""), otp)
            } else if (req.body.sendOtpTo === SIGNING_TYPE.EMAIL) {
                otp = encrypt.generateAuthToken();
                userDetails = await UserModal.findUserByEmail(req.body.email);
                if (!userDetails) {
                    return res.handler.notFound("validation_not_found_email");
                }
                // await UserModal.sendEMailVerificationLink(req.body.email, otp, userDetails._id.toString() ,"Reset Password");
                await UserModal.resetPasswordLinkEmail({ username: userDetails.first_name, reset_link: `${VALUES.WEB_APP_URL}auth/create-new-password?verifier=${otp}&id=${userDetails._id.toString()}` }, userDetails.email,)

                userDetails.forgot_password_otp = otp;
                userDetails.otp_verification_time = new Date();
                userDetails.otp_verification_time_limit = 10;
                await userDetails.save();
            }

            if (req.body.sendOtpTo === SIGNING_TYPE.MOBILE) {
                return res.handler.success("process_sms_sent");
            } else {
                return res.handler.success("process_email_sent");
            }
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async verifyMobileForgotPasswordOTP(req, res) {
        try {
            let userDetails = await UserModal.verifyForgotPasswordOTP(req.body, req.headers);
            if (!userDetails) {
                return res.handler.badRequest("validation_device_incorrect_otp");
            }

            if ((userDetails.country_code + userDetails.mobile_number) !== req.body.mobileNumber) {
                return res.handler.badRequest("validation_device_incorrect_otp");
            }

            if (!userDetails.otp_verification_time_limit || (moment().diff(moment(userDetails.otp_verification_time), "minutes") > userDetails.otp_verification_time_limit)) {
                return res.handler.badRequest("validation_invalid_token_expired");
            }

            return res.handler.success();

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async checkRestPasswordLink(req, res) {
        try {
            let userDetails = await UserModal.verifyForgotPasswordOTP(req.body, req.headers);

            if (!userDetails) {
                return res.handler.badRequest("validation_invalid_token");
            }

            if (userDetails.id !== req.body.id) {
                return res.handler.badRequest("validation_invalid_token");
            }

            if (!userDetails.otp_verification_time_limit || moment().diff(moment(userDetails.otp_verification_time), "minutes") > userDetails.otp_verification_time_limit) {
                userDetails.forgot_password_otp = undefined;
                userDetails.otp_verification_time = undefined;
                userDetails.otp_verification_time_limit = undefined;
                await userDetails.save();
                return res.handler.badRequest("validation_invalid_link_expired");
            }

            return res.handler.success();

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async verifyForgotPassword(req, res) {
        try {
            let userDetails = await UserModal.verifyForgotPasswordOTP(req.body, req.headers);

            if (!userDetails) {
                return res.handler.badRequest("validation_invalid_token");
            }

            if (req.body.type === SIGNING_TYPE.MOBILE && (userDetails.country_code + userDetails.mobile_number) !== req.body.mobileNumber) {
                return res.handler.badRequest("validation_device_incorrect_otp");
            } else if (req.body.type === SIGNING_TYPE.EMAIL && (userDetails.id !== req.body.id)) {
                return res.handler.badRequest("validation_device_incorrect_otp");
            }

            // August 8th call discussion to remove timing related validation while changing password. ( removed validation for mobile type forgot password flow)
            if ((req.body.type === SIGNING_TYPE.EMAIL && moment().diff(moment(userDetails.otp_verification_time), "minutes") > userDetails.otp_verification_time_limit) || !userDetails.otp_verification_time_limit) {
                return res.handler.badRequest("validation_invalid_token_expired");
            }

            await UserModal.updateUserPassword(req.body.newPassword, userDetails);

            userDetails.forgot_password_otp = undefined;
            userDetails.otp_verification_time = undefined;
            userDetails.otp_verification_time_limit = undefined;
            await userDetails.save();
            return res.handler.success("updated_password");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async userRoleAccessed(req, res) {
        try {
            const userRole = await UserModal.getUserRoleById(req.body.userRoleId);

            if (!userRole) {
                return res.handler.notFound("validation_not_found_role");
            }

            if (!userRole.is_active || userRole.is_deleted) {
                return res.handler.badRequest("validation_invalid_user_role")
            }

            // if the token was updated by the authentication middleware, the consider  updatedAccessToken
            const currentTokenValue = req.headers.updatedAccessToken || req.headers.authorization;

            // generate new access token every time profile is switched
            let oldSession = await UserModal.getUserSessionFromAccessToken(currentTokenValue);
            if (oldSession) {
                oldSession.status = VALUES.sessionStatus.CLOSE;
                await oldSession.save();
            }

            if (req.body.fcmToken) {
                await UserRoleDeviceModel.upsertDevice(
                    {
                        device_type: req.headers.devicetype,
                        device_token: req.headers.devicetoken,
                    },
                    {
                        tenant_id: userRole.tenant_id,
                        user_role_id: req.body.userRoleId,
                        fcm_token: req.body.fcmToken
                    }
                )
            }

            if (req.headers.deviceTypeDetected === deviceTypeDetected.PORTAL) {
                const tokens = await UserModal.generateTokenFromRefreshToken(req.headers.refreshtoken);
                let accessToken = tokens.AuthenticationResult?.AccessToken;

                res.setHeader("refreshed-access-token", accessToken);
                let userSession = await UserModal.newUserSession();
                userSession.access_token = accessToken;
                userSession.user_id = userRole.user_id;
                userSession.device_token = req.headers.devicetoken;
                userSession.device_type = req.headers.devicetype;
                userSession.start_time = new Date();
                userSession.status = VALUES.sessionStatus.ACTIVE;
                userSession.total_session_time_in_sec = tokens.AuthenticationResult.ExpiresIn;
                userSession.tenant_id = userRole.tenant_id;
                userSession.user_role_id = userRole._id;
                userSession.collection_name = "users"

                switch (userRole.role_id.portal_type) {
                    case VALUES.portals.TENANT_PORTAL:
                        userSession.tenant_id = userRole.tenant_id;
                        userSession.branch_id = undefined;
                        break;
                    case VALUES.portals.BRANCH_PORTAL:
                        userSession.branch_id = userRole.branch_id;
                        userSession.tenant_id = undefined;
                        break;
                    case VALUES.portals.SYSTEM_PORTAL:
                        userSession.tenant_id = undefined;
                        userSession.branch_id = undefined;
                        break;
                }
                await userSession.save();
            } else if (req.headers.deviceTypeDetected === deviceTypeDetected.APPLICATION) {

                const loginTime = new Date();
                const deviceaccesstype = req.headers.deviceaccesstype;
                //passed current date, as we can only see user roles ( profiles ) after login.
                const accessToken = await UserModal.generateAppAccessTokenFromRefreshToken(req.headers.refreshtoken, userRole, loginTime, deviceaccesstype);

                res.setHeader("refreshed-access-token", accessToken);
                let userSession = await UserModal.newUserSession();
                userSession.access_token = accessToken;
                userSession.user_id = userRole.user_id;
                userSession.device_token = req.headers.devicetoken;
                userSession.device_type = req.headers.devicetype;
                userSession.start_time = new Date();
                userSession.status = VALUES.sessionStatus.ACTIVE;
                userSession.total_session_time_in_sec = process.env.JWT_EXPIRE_IN_HOURS.split('h')[0] * 3600000;
                userSession.tenant_id = userRole.tenant_id;
                userSession.user_role_id = userRole._id;
                userSession.collection_name = userRole.collection_name
                await userSession.save();

                switch (userRole.role_id.portal_type) {

                    case VALUES.portals.CUSTOMER_APP:
                        {
                            if (!req.headers.deviceaccesstype || !req.headers.devicetype) {
                                return res.handler.conflict("deviceaccessType_or_devicetype_missing");
                            }
                            const devices = Array.isArray(userRole.device_access) ? userRole.device_access : [];
                            const deviceIdx = devices.findIndex(d => d.device_id === req.headers.devicetoken);
                            // userSession.tenant_id = undefined;
                            // userSession.branch_id = undefined;

                            // On customer's first time login, made him/her verified.
                            userRole.is_verified = true

                            // update the device_access array if needed with device_id, type, os
                            if (deviceIdx === -1) {
                                if (Array.isArray(userRole.device_access)) {
                                    userRole.device_access.push({ device_id: req.headers.devicetoken, type: req.headers.deviceaccesstype, os: req.headers.devicetype })
                                } else {
                                    userRole.device_access = [{ device_id: req.headers.devicetoken, type: req.headers.deviceaccesstype, os: req.headers.devicetype }]
                                }
                                await userRole.save();
                            }
                            break;
                        }

                    case VALUES.portals.SALES_APP:
                        {
                            //TODO: need to implement multiple device & ask for login into current device or cancel
                            break
                        }
                }

                if (deviceaccesstype === VALUES.deviceAccessType.MOBILE) {
                    if (!userRole.first_mobile_login_time) {
                        userRole.first_mobile_login_time = loginTime;
                    }
                    userRole.last_mobile_login_time = loginTime;
                } else if (deviceaccesstype === VALUES.deviceAccessType.TABLET) {
                    userRole.last_tablet_login_time = loginTime;
                }

                await userRole.save();
            }

            return res.handler.success();
        }
        catch (error) {
            switch (error.name) {
                case "NotAuthorizedException":
                    return res.handler.unauthorized("validation_not_found_valid_refresh_token", undefined, error)

                case "invalid_user_id":
                    return res.handler.unauthorized("validation_not_found_valid_refresh_token", undefined, error);

                default:
                    return res.handler.serverError(error);
            }
        }
    }

    async userRolesList(req, res) {
        try {
            const body = req.query;
            const userDetails = await UserModal.findUserById(body.userId);
            if (!userDetails || userDetails.is_deleted) {
                return res.handler.notFound("validation_not_found_user");
            }

            const userRoles = await UserModal.getNonDeletedPortalUserRolesById(userDetails._id, req.headers.devicetype);

            for (let i = 0; i < userRoles.length; i++) {
                const {
                    profile_pic,
                    tenant_id: {
                        _id: tenantId
                    } = {},
                } = userRoles[i]

                if (profile_pic) {
                    userRoles[i].profile_pic = CommonController.getProfileImageUrl(tenantId) + profile_pic;
                    userRoles[i].profile_thumbnail_pic = CommonController.getProfileThumbImageUrl(tenantId) + profile_pic;
                }
            }

            return res.handler.success(null, userRoles)
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    //TODO disable all the token generated form the given refresh token in awscognito
    async logout(req, res) {
        try {
            const authorization = req.headers.authorization;
            const userSession = await UserModal.getUserSessionFromAccessToken(authorization);
            if (!userSession) {
                return res.handler.unauthorized("validation_not_found_valid_session_details")
            }

            switch (userSession.status) {
                case VALUES.sessionStatus.ACTIVE:
                    userSession.status = VALUES.sessionStatus.LOGOUT;
                    userSession.end_time = new Date();

                    await Promise.all([
                        userSession.save(),
                        UserRoleDeviceModel.deleteDevicesByFilter(
                            {
                                device_type: req.headers.devicetype,
                                device_token: req.headers.devicetoken,
                            }
                        )
                    ])

                    return res.handler.success("process_user_logout");

                case VALUES.sessionStatus.CLOSE:
                    return res.handler.badRequest("validation_invalid_token");

                case VALUES.sessionStatus.LOGOUT:
                    return res.handler.badRequest("validation_expired_token_logout")

                case VALUES.sessionStatus.EXPIRED:
                    return res.handler.badRequest("validation_invalid_token");
            }
            return res.handler.success("process_user_logout");

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getTokenConfigInfo(req, res) {
        try {
            const result = await UserModal.getTokenConfigInfo();
            return res.handler.success(null, result);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async updateTokenConfigInfo(req, res) {
        try {
            const body = req.body;
            const [config, result] = await Promise.all([
                UserModal.getTokenConfigInfo(), UserModal.updateTokenConfigInfo(body, req.headers)
            ]);

            config.access_token_info.time = body.time;
            config.access_token_info.time_unit = body.timeUnit
            await config.save();
            return res.handler.success("updated_edited");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    // Micro-services call it for their authentication
    async serviceAuthenticator(req, res) {
        try {
            res.handler.success(null, {
                userDetails: req.headers.userDetails,
                userRoleDetails: req.headers.sessionDetails.role_id,
                sessionDetails: req.headers.sessionDetails,
                tenantDetail: req.headers.tenantDetail
            });

        } catch (error) {
            return res.handler.serverError(error);
        }
    }
}

module.exports = AuthController;
